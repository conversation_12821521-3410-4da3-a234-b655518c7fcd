import os
import time
import random
import sys
try:
    import msvcrt
    WINDOWS = True
except ImportError:
    # 如果不是Windows系统，使用替代方案
    msvcrt = None
    WINDOWS = False

class Tetromino:
    """俄罗斯方块类"""
    
    # 定义7种方块形状
    SHAPES = {
        'I': [['.....',
               '..#..',
               '..#..',
               '..#..',
               '..#..'],
              ['.....',
               '.....',
               '####.',
               '.....',
               '.....']],
        
        'O': [['.....',
               '.....',
               '.##..',
               '.##..',
               '.....']],
        
        'T': [['.....',
               '.....',
               '.#...',
               '###..',
               '.....'],
              ['.....',
               '.....',
               '.#...',
               '.##..',
               '.#...'],
              ['.....',
               '.....',
               '.....',
               '###..',
               '.#...'],
              ['.....',
               '.....',
               '.#...',
               '##...',
               '.#...']],
        
        'S': [['.....',
               '.....',
               '.##..',
               '##...',
               '.....'],
              ['.....',
               '.#...',
               '.##..',
               '..#..',
               '.....']],
        
        'Z': [['.....',
               '.....',
               '##...',
               '.##..',
               '.....'],
              ['.....',
               '..#..',
               '.##..',
               '.#...',
               '.....']],
        
        'J': [['.....',
               '.#...',
               '.#...',
               '##...',
               '.....'],
              ['.....',
               '.....',
               '#....',
               '###..',
               '.....'],
              ['.....',
               '.##..',
               '.#...',
               '.#...',
               '.....'],
              ['.....',
               '.....',
               '###..',
               '..#..',
               '.....']],
        
        'L': [['.....',
               '..#..',
               '..#..',
               '.##..',
               '.....'],
              ['.....',
               '.....',
               '###..',
               '#....',
               '.....'],
              ['.....',
               '##...',
               '.#...',
               '.#...',
               '.....'],
              ['.....',
               '.....',
               '..#..',
               '###..',
               '.....']]
    }
    
    def __init__(self, shape_type=None):
        if shape_type is None:
            shape_type = random.choice(list(self.SHAPES.keys()))
        
        self.shape_type = shape_type
        self.shape = self.SHAPES[shape_type]
        self.rotation = 0
        self.x = 3  # 起始x位置
        self.y = 0  # 起始y位置
    
    def get_rotated_shape(self, rotation=None):
        """获取旋转后的形状"""
        if rotation is None:
            rotation = self.rotation
        return self.shape[rotation % len(self.shape)]
    
    def rotate(self):
        """旋转方块"""
        self.rotation = (self.rotation + 1) % len(self.shape)

class GameBoard:
    """游戏板类"""
    
    def __init__(self, width=10, height=20):
        self.width = width
        self.height = height
        self.board = [['.' for _ in range(width)] for _ in range(height)]
        self.score = 0
        self.lines_cleared = 0
        self.level = 1
        self.fall_time = 0
        self.fall_speed = 500  # 毫秒
    
    def is_valid_position(self, tetromino, dx=0, dy=0, rotation=None):
        """检查方块位置是否有效"""
        shape = tetromino.get_rotated_shape(rotation)
        
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    new_x = tetromino.x + x + dx
                    new_y = tetromino.y + y + dy
                    
                    # 检查边界
                    if (new_x < 0 or new_x >= self.width or 
                        new_y >= self.height):
                        return False
                    
                    # 检查是否与已有方块重叠
                    if new_y >= 0 and self.board[new_y][new_x] != '.':
                        return False
        
        return True
    
    def place_tetromino(self, tetromino):
        """放置方块到游戏板上"""
        shape = tetromino.get_rotated_shape()
        
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    board_x = tetromino.x + x
                    board_y = tetromino.y + y
                    if board_y >= 0:
                        self.board[board_y][board_x] = '#'
    
    def clear_lines(self):
        """清除完整的行"""
        lines_to_clear = []
        
        for y in range(self.height):
            if all(cell != '.' for cell in self.board[y]):
                lines_to_clear.append(y)
        
        # 清除行并添加新的空行
        for y in lines_to_clear:
            del self.board[y]
            self.board.insert(0, ['.' for _ in range(self.width)])
        
        # 更新分数
        if lines_to_clear:
            self.lines_cleared += len(lines_to_clear)
            self.score += len(lines_to_clear) * 100 * self.level
            self.level = self.lines_cleared // 10 + 1
            self.fall_speed = max(50, 500 - (self.level - 1) * 50)
        
        return len(lines_to_clear)
    
    def is_game_over(self):
        """检查游戏是否结束"""
        return any(cell != '.' for cell in self.board[0])

class TetrisGame:
    """俄罗斯方块游戏主类"""

    def __init__(self):
        self.board = GameBoard()
        self.current_piece = Tetromino()
        self.next_piece = Tetromino()
        self.game_over = False
        self.paused = False
        self.last_fall_time = time.time() * 1000

    def clear_screen(self):
        """清屏"""
        if WINDOWS:
            os.system('cls')
        else:
            os.system('clear')

    def get_key_input(self):
        """获取键盘输入（非阻塞）"""
        if WINDOWS and msvcrt and msvcrt.kbhit():
            try:
                key = msvcrt.getch()
                if isinstance(key, bytes):
                    key = key.decode('utf-8', errors='ignore')
                return key.lower()
            except:
                return None
        return None

    def draw_board_with_piece(self):
        """绘制游戏板和当前方块"""
        # 创建临时游戏板
        display_board = [row[:] for row in self.board.board]

        # 添加当前方块到显示板
        shape = self.current_piece.get_rotated_shape()
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    board_x = self.current_piece.x + x
                    board_y = self.current_piece.y + y
                    if (0 <= board_x < self.board.width and
                        0 <= board_y < self.board.height):
                        display_board[board_y][board_x] = '#'

        return display_board

    def display(self):
        """显示游戏界面"""
        self.clear_screen()

        display_board = self.draw_board_with_piece()

        print("=" * 30)
        print("       俄罗斯方块")
        print("=" * 30)
        print(f"分数: {self.board.score}  等级: {self.board.level}  行数: {self.board.lines_cleared}")
        print("-" * 22)

        # 显示游戏板
        for row in display_board:
            print("|" + "".join("█" if cell == '#' else " " for cell in row) + "|")

        print("-" * 22)

        # 显示下一个方块
        print("下一个:")
        next_shape = self.next_piece.get_rotated_shape()
        for row in next_shape:
            print("".join("█" if cell == '#' else " " for cell in row))

        print("\n控制:")
        print("A/D - 左右移动")
        print("S - 快速下降")
        print("W - 旋转")
        print("P - 暂停")
        print("Q - 退出")

        if self.game_over:
            print("\n游戏结束! 按任意键退出...")
        elif self.paused:
            print("\n游戏暂停中...")

    def move_piece(self, dx, dy):
        """移动方块"""
        if self.board.is_valid_position(self.current_piece, dx, dy):
            self.current_piece.x += dx
            self.current_piece.y += dy
            return True
        return False

    def rotate_piece(self):
        """旋转方块"""
        new_rotation = (self.current_piece.rotation + 1) % len(self.current_piece.shape)
        if self.board.is_valid_position(self.current_piece, rotation=new_rotation):
            self.current_piece.rotate()
            return True
        return False

    def drop_piece(self):
        """方块自然下降"""
        if not self.move_piece(0, 1):
            # 方块无法继续下降，放置到游戏板
            self.board.place_tetromino(self.current_piece)
            self.board.clear_lines()

            # 生成新方块
            self.current_piece = self.next_piece
            self.next_piece = Tetromino()

            # 检查游戏是否结束
            if self.board.is_game_over():
                self.game_over = True

    def handle_input(self):
        """处理用户输入"""
        key = self.get_key_input()

        if key == 'q':
            return False
        elif key == 'p':
            self.paused = not self.paused
        elif not self.paused and not self.game_over:
            if key == 'a':
                self.move_piece(-1, 0)
            elif key == 'd':
                self.move_piece(1, 0)
            elif key == 's':
                self.drop_piece()
            elif key == 'w':
                self.rotate_piece()

        return True

    def update(self):
        """更新游戏状态"""
        if self.paused or self.game_over:
            return

        current_time = time.time() * 1000
        if current_time - self.last_fall_time > self.board.fall_speed:
            self.drop_piece()
            self.last_fall_time = current_time

    def run(self):
        """运行游戏主循环"""
        print("俄罗斯方块游戏启动中...")
        print("按任意键开始游戏...")

        # 等待用户按键开始
        if WINDOWS and msvcrt:
            msvcrt.getch()
        else:
            input()

        while True:
            self.display()

            if not self.handle_input():
                break

            self.update()
            time.sleep(0.1)  # 控制游戏循环速度

        print("感谢游玩!")

def main():
    """主函数"""
    try:
        game = TetrisGame()
        game.run()
    except KeyboardInterrupt:
        print("\n游戏被中断")
    except Exception as e:
        print(f"游戏出现错误: {e}")

if __name__ == "__main__":
    main()
