import os
import time
import random
import sys

# 简化版俄罗斯方块，用于测试
class SimpleTetris:
    def __init__(self):
        self.width = 10
        self.height = 20
        self.board = [['.' for _ in range(self.width)] for _ in range(self.height)]
        self.score = 0
        
        # 简单的方块形状（只用I形状测试）
        self.current_piece = {
            'shape': [
                [1, 1, 1, 1]
            ],
            'x': 3,
            'y': 0
        }
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def display(self):
        """显示游戏界面"""
        self.clear_screen()
        
        print("=" * 30)
        print("    简化版俄罗斯方块测试")
        print("=" * 30)
        print(f"分数: {self.score}")
        print("-" * (self.width + 2))
        
        # 创建显示板
        display_board = [row[:] for row in self.board]
        
        # 添加当前方块
        piece = self.current_piece
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j
                    y = piece['y'] + i
                    if 0 <= x < self.width and 0 <= y < self.height:
                        display_board[y][x] = '#'
        
        # 显示游戏板
        for row in display_board:
            print("|" + "".join("█" if cell == '#' else " " for cell in row) + "|")
        
        print("-" * (self.width + 2))
        print("控制: A-左 D-右 S-下 Q-退出")
        print("按回车键继续...")
    
    def move_piece(self, dx, dy):
        """移动方块"""
        new_x = self.current_piece['x'] + dx
        new_y = self.current_piece['y'] + dy
        
        # 简单的边界检查
        if (0 <= new_x <= self.width - 4 and 
            0 <= new_y < self.height - 1):
            self.current_piece['x'] = new_x
            self.current_piece['y'] = new_y
            return True
        return False
    
    def run(self):
        """运行游戏"""
        print("简化版俄罗斯方块启动!")
        print("这是一个测试版本，用于验证基本显示功能")
        input("按回车键开始...")
        
        for step in range(10):  # 只运行10步用于测试
            self.display()
            
            # 获取用户输入
            try:
                user_input = input().lower()
                if user_input == 'q':
                    break
                elif user_input == 'a':
                    self.move_piece(-1, 0)
                elif user_input == 'd':
                    self.move_piece(1, 0)
                elif user_input == 's':
                    self.move_piece(0, 1)
                else:
                    # 自动下降
                    if not self.move_piece(0, 1):
                        # 重置方块位置
                        self.current_piece['x'] = 3
                        self.current_piece['y'] = 0
                        self.score += 10
            except KeyboardInterrupt:
                break
        
        print("测试完成!")

def main():
    try:
        game = SimpleTetris()
        game.run()
    except Exception as e:
        print(f"错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
