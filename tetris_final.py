import time
import random

# 俄罗斯方块 - 最终版本（无清屏）
class TetrisFinal:
    def __init__(self):
        self.width = 10
        self.height = 15  # 减小高度以适应控制台
        self.board = [['.' for _ in range(self.width)] for _ in range(self.height)]
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        
        # 定义方块形状
        self.shapes = {
            'I': [[1, 1, 1, 1]],
            'O': [[1, 1], [1, 1]],
            'T': [[0, 1, 0], [1, 1, 1]],
            'L': [[1, 0], [1, 0], [1, 1]],
            'J': [[0, 1], [0, 1], [1, 1]],
            'S': [[0, 1, 1], [1, 1, 0]],
            'Z': [[1, 1, 0], [0, 1, 1]]
        }
        
        self.current_piece = self.create_new_piece()
        self.next_piece = self.create_new_piece()
    
    def create_new_piece(self):
        """创建新方块"""
        shape_name = random.choice(list(self.shapes.keys()))
        shape = self.shapes[shape_name]
        return {
            'shape': shape,
            'x': self.width // 2 - len(shape[0]) // 2,
            'y': 0,
            'name': shape_name
        }
    
    def is_valid_position(self, piece, dx=0, dy=0):
        """检查位置是否有效"""
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j + dx
                    y = piece['y'] + i + dy
                    
                    # 检查边界
                    if x < 0 or x >= self.width or y >= self.height:
                        return False
                    
                    # 检查是否与已有方块重叠
                    if y >= 0 and self.board[y][x] != '.':
                        return False
        return True
    
    def place_piece(self, piece):
        """放置方块到游戏板"""
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j
                    y = piece['y'] + i
                    if y >= 0:
                        self.board[y][x] = '#'
    
    def clear_lines(self):
        """清除完整的行"""
        lines_to_clear = []
        
        for y in range(self.height):
            if all(cell != '.' for cell in self.board[y]):
                lines_to_clear.append(y)
        
        # 清除行
        for y in lines_to_clear:
            del self.board[y]
            self.board.insert(0, ['.' for _ in range(self.width)])
        
        if lines_to_clear:
            self.lines_cleared += len(lines_to_clear)
            self.score += len(lines_to_clear) * 100 * self.level
            self.level = self.lines_cleared // 10 + 1
        
        return len(lines_to_clear)
    
    def display(self, step):
        """显示游戏界面"""
        # 创建显示板
        display_board = [row[:] for row in self.board]
        
        # 添加当前方块
        piece = self.current_piece
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j
                    y = piece['y'] + i
                    if 0 <= x < self.width and 0 <= y < self.height:
                        display_board[y][x] = '█'
        
        print(f"\n{'='*50}")
        print(f"           俄罗斯方块游戏 - 步骤 {step}")
        print(f"{'='*50}")
        print(f"分数: {self.score:6d} | 等级: {self.level} | 消除行数: {self.lines_cleared}")
        print(f"当前方块: {self.current_piece['name']} | 下一个: {self.next_piece['name']}")
        print("-" * (self.width + 2))
        
        # 显示游戏板
        for row in display_board:
            print("|" + "".join("█" if cell == '#' or cell == '█' else " " for cell in row) + "|")
        
        print("-" * (self.width + 2))
    
    def move_piece(self, dx, dy):
        """移动方块"""
        if self.is_valid_position(self.current_piece, dx, dy):
            self.current_piece['x'] += dx
            self.current_piece['y'] += dy
            return True
        return False
    
    def rotate_piece(self):
        """旋转方块（简化版）"""
        # 简单的旋转逻辑
        old_shape = self.current_piece['shape']
        if len(old_shape) > 1 and len(old_shape[0]) > 1:
            # 90度旋转
            rotated = [[old_shape[j][i] for j in range(len(old_shape)-1, -1, -1)] 
                      for i in range(len(old_shape[0]))]
            
            # 临时设置旋转后的形状
            temp_piece = self.current_piece.copy()
            temp_piece['shape'] = rotated
            
            if self.is_valid_position(temp_piece):
                self.current_piece['shape'] = rotated
                return True
        return False
    
    def run_game(self):
        """运行游戏"""
        print("俄罗斯方块游戏开始!")
        print("这是一个自动演示版本，展示游戏玩法")
        print("方块会自动下降，偶尔会左右移动和旋转")
        
        step_count = 0
        
        try:
            while step_count < 30:  # 运行30步
                self.display(step_count + 1)
                
                # 随机操作
                action = random.random()
                if action < 0.1:  # 10%概率左移
                    if self.move_piece(-1, 0):
                        print("← 左移")
                elif action < 0.2:  # 10%概率右移
                    if self.move_piece(1, 0):
                        print("→ 右移")
                elif action < 0.3:  # 10%概率旋转
                    if self.rotate_piece():
                        print("↻ 旋转")
                
                # 尝试下降
                if not self.move_piece(0, 1):
                    print("↓ 方块着陆")
                    # 方块无法下降，放置它
                    self.place_piece(self.current_piece)
                    lines_cleared = self.clear_lines()
                    
                    if lines_cleared > 0:
                        print(f"🎉 消除了 {lines_cleared} 行! 得分 +{lines_cleared * 100 * self.level}")
                    
                    # 创建新方块
                    self.current_piece = self.next_piece
                    self.next_piece = self.create_new_piece()
                    
                    # 检查游戏是否结束
                    if not self.is_valid_position(self.current_piece):
                        print("\n💀 游戏结束! 新方块无法放置")
                        break
                else:
                    print("↓ 下降")
                
                time.sleep(1)  # 每步暂停1秒
                step_count += 1
            
            if step_count >= 30:
                print(f"\n🎊 演示完成! 最终分数: {self.score}")
            
        except KeyboardInterrupt:
            print("\n⏹️ 游戏被中断")
        except Exception as e:
            print(f"\n❌ 游戏出现错误: {e}")

def main():
    print("🎮 欢迎来到俄罗斯方块!")
    print("=" * 50)
    
    game = TetrisFinal()
    game.run_game()
    
    print("\n" + "=" * 50)
    print("感谢游玩俄罗斯方块! 👋")

if __name__ == "__main__":
    main()
