import os
import time
import random

# 俄罗斯方块演示版本 - 自动运行
class TetrisDemo:
    def __init__(self):
        self.width = 10
        self.height = 20
        self.board = [['.' for _ in range(self.width)] for _ in range(self.height)]
        self.score = 0
        self.level = 1
        
        # 定义方块形状
        self.shapes = {
            'I': [[1, 1, 1, 1]],
            'O': [[1, 1], [1, 1]],
            'T': [[0, 1, 0], [1, 1, 1]],
            'L': [[1, 0], [1, 0], [1, 1]],
            'J': [[0, 1], [0, 1], [1, 1]],
            'S': [[0, 1, 1], [1, 1, 0]],
            'Z': [[1, 1, 0], [0, 1, 1]]
        }
        
        self.current_piece = self.create_new_piece()
    
    def create_new_piece(self):
        """创建新方块"""
        shape_name = random.choice(list(self.shapes.keys()))
        shape = self.shapes[shape_name]
        return {
            'shape': shape,
            'x': self.width // 2 - len(shape[0]) // 2,
            'y': 0,
            'name': shape_name
        }
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def is_valid_position(self, piece, dx=0, dy=0):
        """检查位置是否有效"""
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j + dx
                    y = piece['y'] + i + dy
                    
                    # 检查边界
                    if x < 0 or x >= self.width or y >= self.height:
                        return False
                    
                    # 检查是否与已有方块重叠
                    if y >= 0 and self.board[y][x] != '.':
                        return False
        return True
    
    def place_piece(self, piece):
        """放置方块到游戏板"""
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j
                    y = piece['y'] + i
                    if y >= 0:
                        self.board[y][x] = '#'
    
    def clear_lines(self):
        """清除完整的行"""
        lines_cleared = 0
        y = self.height - 1
        
        while y >= 0:
            if all(cell != '.' for cell in self.board[y]):
                # 清除这一行
                del self.board[y]
                self.board.insert(0, ['.' for _ in range(self.width)])
                lines_cleared += 1
            else:
                y -= 1
        
        if lines_cleared > 0:
            self.score += lines_cleared * 100 * self.level
            self.level = self.score // 1000 + 1
        
        return lines_cleared
    
    def display(self):
        """显示游戏界面"""
        self.clear_screen()
        
        # 创建显示板
        display_board = [row[:] for row in self.board]
        
        # 添加当前方块
        piece = self.current_piece
        for i, row in enumerate(piece['shape']):
            for j, cell in enumerate(row):
                if cell == 1:
                    x = piece['x'] + j
                    y = piece['y'] + i
                    if 0 <= x < self.width and 0 <= y < self.height:
                        display_board[y][x] = '█'
        
        print("=" * 40)
        print("           俄罗斯方块演示")
        print("=" * 40)
        print(f"分数: {self.score:6d}    等级: {self.level}")
        print(f"当前方块: {self.current_piece['name']}")
        print("-" * (self.width + 2))
        
        # 显示游戏板
        for row in display_board:
            print("|" + "".join("█" if cell == '#' or cell == '█' else " " for cell in row) + "|")
        
        print("-" * (self.width + 2))
        print("自动演示中... (Ctrl+C 退出)")
    
    def move_piece(self, dx, dy):
        """移动方块"""
        if self.is_valid_position(self.current_piece, dx, dy):
            self.current_piece['x'] += dx
            self.current_piece['y'] += dy
            return True
        return False
    
    def run_demo(self):
        """运行演示"""
        print("俄罗斯方块演示启动!")
        print("这是一个自动演示版本")
        time.sleep(2)
        
        step_count = 0
        
        try:
            while step_count < 50:  # 运行50步演示
                self.display()
                
                # 随机移动
                if random.random() < 0.3:  # 30%概率左右移动
                    direction = random.choice([-1, 1])
                    self.move_piece(direction, 0)
                
                # 尝试下降
                if not self.move_piece(0, 1):
                    # 方块无法下降，放置它
                    self.place_piece(self.current_piece)
                    lines_cleared = self.clear_lines()
                    
                    if lines_cleared > 0:
                        print(f"\n消除了 {lines_cleared} 行!")
                        time.sleep(1)
                    
                    # 创建新方块
                    self.current_piece = self.create_new_piece()
                    
                    # 检查游戏是否结束
                    if not self.is_valid_position(self.current_piece):
                        print("\n游戏结束!")
                        break
                
                time.sleep(0.5)  # 控制演示速度
                step_count += 1
            
            print(f"\n演示完成! 最终分数: {self.score}")
            
        except KeyboardInterrupt:
            print("\n演示被中断")
        except Exception as e:
            print(f"\n演示出现错误: {e}")

def main():
    demo = TetrisDemo()
    demo.run_demo()
    print("感谢观看演示!")

if __name__ == "__main__":
    main()
